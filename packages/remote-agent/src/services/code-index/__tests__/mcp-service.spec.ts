import { ServiceFactory } from '../service-factory';
import { MCPManager } from '../interfaces/manager';

describe('MCP Service', () => {
  let manager: MCPManager;

  beforeEach(() => {
    manager = ServiceFactory.createMCPManager();
  });

  it('should initialize successfully', async () => {
    await expect(manager.initialize()).resolves.not.toThrow();
  });

  it('should index codebase without errors', async () => {
    await manager.initialize();
    await expect(manager.indexCodebase()).resolves.not.toThrow();
  });
});